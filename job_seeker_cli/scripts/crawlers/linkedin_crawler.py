# /Users/<USER>/Desktop/crawl4ai/tools/linkedin_jobs_crawler.py
# 此脚本使用基于身份的爬取方式实现LinkedIn职位爬虫
# 目的是从LinkedIn搜索结果中提取职位信息

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, BrowserProfiler
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

# 常量
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "linkedin_results")
os.makedirs(OUTPUT_DIR, exist_ok=True)

class LinkedInJobsCrawler:
    """
    LinkedIn职位爬虫，使用基于身份的爬取方式
    以真实用户的身份访问内容。
    """
    
    def __init__(
        self,
        search_url: str,
        output_dir: str = OUTPUT_DIR,
        profile_name: str = "linkedin-browser-profile",
        headless: bool = False,
        pages_to_crawl: int = 2,  # 默认爬取2页
        results_per_page: int = 25  # LinkedIn默认每页25条结果
    ):
        self.search_url = search_url
        self.output_dir = output_dir
        self.profile_name = profile_name
        self.headless = headless
        self.pages_to_crawl = pages_to_crawl
        self.results_per_page = results_per_page
        self.jobs = []
        
    async def setup_browser_profile(self) -> str:
        """为LinkedIn设置浏览器配置文件，如果不存在则创建一个。"""
        print("为LinkedIn设置浏览器配置文件...")
        
        # 创建配置文件实例
        # BrowserProfiler不接受profiles_dir作为参数
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        profiles_dir = os.path.join(project_root, "browser_profiles")
        os.makedirs(profiles_dir, exist_ok=True)
        
        # 创建配置文件实例，不指定profiles_dir
        profiler = BrowserProfiler()
        
        # 检查我们是否已经有配置文件
        existing_profiles = profiler.list_profiles()
        profile_path = None
        
        for profile in existing_profiles:
            if profile['name'] == self.profile_name:
                profile_path = profile['path']
                print(f"使用现有配置文件: {self.profile_name} 位于 {profile_path}")
                break
        
        # 如果没找到配置文件，创建一个
        if not profile_path:
            print(f"创建新的浏览器配置文件: {self.profile_name} 位于 {profiles_dir}")
            print("请在打开的浏览器窗口中登录LinkedIn...")
            print("完成后，在终端按'q'保存配置文件。")
            
            # 创建配置文件，不使用profiles_dir参数
            # 设置环境变量来指定配置文件目录
            os.environ["CRAWL4AI_PROFILES_DIR"] = profiles_dir
            profile_path = await profiler.create_profile(
                profile_name=self.profile_name
            )
            
            print(f"配置文件保存在: {profile_path}")
        
        return profile_path
    
    async def extract_job_info(self, url: str = None, job_card_selector: str = "li.jobs-search-results__list-item") -> List[Dict[str, Any]]:
        """
        从LinkedIn职位搜索结果中提取职位信息。
        
        Args:
            url: 要爬取的URL，如果为None则使用初始化时设置的URL
            job_card_selector: 职位卡片的CSS选择器
            
        Returns:
            包含职位信息的字典列表
        """
        # 如果传入了特定URL，则使用该URL，否则使用初始化时设置的URL
        target_url = url if url else self.search_url
        # 首先，设置浏览器配置文件
        profile_path = await self.setup_browser_profile()
        
        # 配置带有身份验证的浏览器设置
        browser_config = BrowserConfig(
            headless=self.headless,      # 设置为False可以看到浏览器
            use_managed_browser=True,
            user_data_dir=profile_path,
            browser_type="chromium",
            verbose=True
        )
        
        # 根据文档配置爬虫
        # 参考: https://docs.crawl4ai.com/advanced/lazy-loading/
        from crawl4ai.async_configs import CacheMode
        
        # 定义LinkedIn职位列表的提取模式
        schema = {
            "name": "LinkedIn Job Listings",
            # 尝试多种选择器，因为LinkedIn可能会更改其结构
            "baseSelector": "li.jobs-search-results__list-item, div.job-search-card, .job-card-container, .job-card-list__entity, .jobs-search-results__list-item, .scaffold-layout__list-container div.jobs-search-results-list__list-item",
            "fields": [
                {
                    "name": "jobTitle",
                    "selector": ".job-card-list__title, h3.base-search-card__title, .job-card-container__link",
                    "type": "text"
                },
                {
                    "name": "jobAdvertiser",
                    "selector": ".job-card-container__company-name, h4.base-search-card__subtitle, .job-card-container__primary-description, .job-card-container__subtitle-link, span.job-card-container__primary-description",
                    "type": "text"
                },
                {
                    "name": "location",
                    "selector": ".job-card-container__metadata-item, span.job-search-card__location, .job-card-container__metadata-wrapper",
                    "type": "text"
                },
                {
                    "name": "listDate",
                    "selector": "time.job-search-card__listdate, .job-card-container__listed-time",
                    "type": "attribute",
                    "attribute": "datetime",
                    "fallback": {
                        "type": "text"
                    }
                },
                {
                    "name": "recommendedJobLink",
                    "selector": "a.job-card-container__link, a.base-card__full-link, a.job-card-list__title",
                    "type": "attribute",
                    "attribute": "href"
                }
            ]
        }
        
        # 完整优化的爬虫配置，基于core_simple-crawling.md和lazy-loading.md文档
        crawl_config = CrawlerRunConfig(
            # 等待职位列表元素出现 - 增加等待时间和选择器范围
            wait_for=".jobs-search-results__list-item, .job-search-card, .job-card-container, ul.jobs-search-results__list, .scaffold-layout__list-container, .jobs-search-results-list",
            page_timeout=180000,  # 增加页面超时时间至3分钟          
            # 不使用scan_full_page，而是通过自定义JavaScript滚动左侧列表容器
            wait_for_images=True,    # 等待图片加载完成
            scan_full_page=False,    # 不使用默认滚动
            scroll_delay=0,          # 禁用默认滚动延迟
            

            
            # 采用V4版智能滚动脚本，通过比较相对位置选择最左侧容器
            js_code=[
                """
                console.log('🚀 开始执行V4版智能滚动脚本(基于相对位置判断)...');

                // --- PART 1: 动态查找所有潜在的可滚动容器 (逻辑不变) ---
                console.log('步骤1: 正在动态扫描页面，寻找所有可滚动的容器...');
                const allElements = document.querySelectorAll('*');
                const scrollableCandidates = [];
                allElements.forEach(element => {
                    try {
                        const style = window.getComputedStyle(element);
                        const isScrollable = style.overflow === 'auto' || style.overflow === 'scroll' ||
                                             style.overflowY === 'auto' || style.overflowY === 'scroll';
                        const hasVerticalScrollbar = element.scrollHeight > element.clientHeight;
                        if (isScrollable && hasVerticalScrollbar && element.clientHeight > (window.innerHeight / 3)) {
                            scrollableCandidates.push(element);
                        }
                    } catch(e) {}
                });
                if (scrollableCandidates.length === 0) {
                    console.warn("标准方法未找到滚动容器，启用备用查找逻辑...");
                    allElements.forEach(element => {
                        try {
                            if (element.scrollHeight > window.innerHeight && element.clientHeight > 0) {
                                if (element.tagName !== 'HTML' && element.tagName !== 'BODY') {
                                    scrollableCandidates.push(element);
                                }
                            }
                        } catch(e) {}
                    });
                }
                console.log(`找到了 ${scrollableCandidates.length} 个候选滚动容器。`);

                // --- PART 2: 基于相对位置选举最佳滚动容器 (全新可靠逻辑) ---
                console.log('步骤2: 开始基于相对位置选举最佳容器...');
                let bestContainer = null;

                if (scrollableCandidates.length === 0) {
                    console.warn('⚠️ 未找到任何候选滚动容器，将尝试滚动整个页面。');
                    bestContainer = document.documentElement;
                } else if (scrollableCandidates.length === 1) {
                    console.log('✅ 只找到一个候选容器，直接选择它。');
                    bestContainer = scrollableCandidates[0];
                } else {
                    console.log(`找到 ${scrollableCandidates.length} 个候选容器，将通过比较它们的左侧坐标来选择最左边的一个。`);
                    
                    let leftmostContainer = null;
                    let minLeft = Infinity; // 初始化一个极大值

                    scrollableCandidates.forEach(container => {
                        const rect = container.getBoundingClientRect();
                        const className = container.className && typeof container.className === 'string' ? container.className.slice(0, 50) : '';
                        console.log(`评估候选者: ${container.tagName}, 类名: ${className}, 左侧位置(left): ${rect.left}px`);

                        if (rect.left < minLeft) {
                            minLeft = rect.left;
                            leftmostContainer = container;
                        }
                    });
                    
                    bestContainer = leftmostContainer;
                }

                if (bestContainer && bestContainer !== document.documentElement) {
                    const finalRect = bestContainer.getBoundingClientRect();
                    console.log(`✅ 成功选举出最左侧的容器! 左侧位置: ${finalRect.left}px, 滚动高度: ${bestContainer.scrollHeight}px`);
                    bestContainer.style.outline = '3px solid blue'; // 高亮选中的容器
                }

                // --- PART 3: 执行滚动操作 (逻辑不变) ---
                function scrollPage() {
                    console.log('步骤3: 开始分段滚动页面...');
                    let scrollCount = 0;
                    const maxScrolls = 20;
                    let lastJobCount = 0;
                    let sameCountTimes = 0;

                    function doScroll() {
                        const currentJobCount = document.querySelectorAll('li.jobs-search-results__list-item, .job-card-container').length;
                        if (scrollCount >= maxScrolls || sameCountTimes >= 3) {
                            console.log('滚动结束。原因: ' + (scrollCount >= maxScrolls ? '达到最大滚动次数' : '职位数量连续3次未变'));
                            console.log('最终职位数量: ' + currentJobCount);
                            return;
                        }
                        scrollCount++;
                        console.log(`滚动第 ${scrollCount} 次... 当前职位数: ${currentJobCount}`);
                        bestContainer.scrollTo(0, bestContainer.scrollTop + bestContainer.clientHeight * 0.8);
                        if (currentJobCount === lastJobCount) {
                            sameCountTimes++;
                        } else {
                            sameCountTimes = 0;
                        }
                        lastJobCount = currentJobCount;
                        setTimeout(doScroll, 3000);
                    }
                    doScroll();
                }
                
                setTimeout(scrollPage, 2000);
                """
            ],
            
            # 其他参数
            delay_before_return_html=10,   # 执行JS后等待60秒再获取HTML - 大幅增加时间确保滚动完成
            
            # 内容过滤优化 - 降低过滤条件，确保捕获更多内容
            word_count_threshold=3,   # 降低阈值，保留至少有3个词的内容块
            excluded_tags=['style', 'script'],  # 仅排除样式和脚本标签
            exclude_external_links=False,  # 处理外部链接，以获取更多数据
            process_iframes=True,         # 处理iframe中的内容
            
            # 界面清理
            remove_overlay_elements=True,  # 移除弹窗和覆盖元素
            
            # 缓存控制
            cache_mode=CacheMode.BYPASS,   # 每次爬取新内容
            
            # 添加提取策略 - 直接在初始爬取中使用
            extraction_strategy=JsonCssExtractionStrategy(schema, verbose=True),
            
            # 调试信息
            verbose=True
        )
        
        print(f"开始从以下链接爬取LinkedIn职位: {self.search_url}")
        
        try:
            # 使用我们的浏览器配置创建爬虫
            async with AsyncWebCrawler(config=browser_config) as crawler:
                # 打印详细信息
                print(f"正在连接到: {target_url}")
                
                # 运行爬虫与提取策略
                result = await crawler.arun(url=target_url, config=crawl_config)
                
                # 详细错误处理
                if not result.success:
                    print(f"爬取 {self.search_url} 时出错: {result.error_message}")
                    return []
                
                print("成功加载页面。正在提取职位信息...")

                # 检查结果是否包含HTML内容
                if not hasattr(result, 'html') or not result.html:
                    print("错误: 结果中没有找到HTML内容")
                    return []
                
                # 检查提取是否成功
                if not hasattr(result, 'extracted_content') or not result.extracted_content:
                    print("未找到提取内容。提取可能失败。")
                    return []
                
                # 解析提取的JSON
                try:
                    job_data = json.loads(result.extracted_content)
                    
                    # 后处理职位数据，从链接中提取职位ID
                    for job in job_data:
                        # 使用正则表达式从链接中提取职位ID
                        if job.get('link'):
                            # 如果链接是相对路径，添加LinkedIn域名前缀
                            if job['link'].startswith('/'):
                                job['link'] = f"https://www.linkedin.com{job['link']}"
                            
                            import re
                            match = re.search(r'view/(\d+)', job['link']) or re.search(r'jobs/view/(\d+)', job['link'])
                            if match:
                                job['jobId'] = match.group(1)
                            else:
                                job['jobId'] = None
                        
                        # 添加爬取时间戳
                        job['crawledAt'] = datetime.now().isoformat()
                    
                    print(f"成功提取 {len(job_data)} 个职位列表")
                    return job_data
                except json.JSONDecodeError as e:
                    print(f"解析提取内容时出错: {e}")
                    print(f"原始提取内容: {result.extracted_content[:100]}...")
                    return []
        except Exception as e:
            print(f"爬取过程中出错: {e}")
            return []
    
    async def save_results(self, jobs: List[Dict[str, Any]]):
        """保存提取的职位信息到JSON文件。"""
        if not jobs:
            print("没有职位可保存。")
            return
        
        # 创建带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"linkedin_jobs_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        # 保存到JSON
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump({
                "search_url": self.search_url,
                "crawled_at": datetime.now().isoformat(),
                "job_count": len(jobs),
                "jobs": jobs
            }, f, indent=2)
        
        print(f"已保存 {len(jobs)} 个职位列表到 {filepath}")
        
        # 同时保存一个概要的markdown文件
        summary_path = os.path.join(self.output_dir, f"linkedin_jobs_summary_{timestamp}.md")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"# LinkedIn职位爬取结果\n\n")
            f.write(f"爬取完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"搜索URL: {self.search_url}\n")
            f.write(f"找到的职位总数: {len(jobs)}\n\n")
            
            f.write("## 职位列表\n\n")
            for i, job in enumerate(jobs, 1):
                # 使用get方法安全获取字段，提供默认值防止KeyError
                title = job.get('jobTitle', '未知职位')
                f.write(f"{i}. **{title}**\n")
                
                # 处理可能缺失的company字段
                company = job.get('jobAdvertiser', '未知公司')
                f.write(f"   - 公司: {company}\n")
                
                location = job.get('location', '未知地点')
                f.write(f"   - 地点: {location}\n")
                
                link = job.get('recommendedJobLink')
                if link:
                    # 确保链接是完整的URL
                    if not link.startswith('http'):
                        link = f"https://www.linkedin.com{link}"
                    f.write(f"   - [查看职位]({link})\n")
                
                list_date = job.get('listDate')
                f.write(f"   - 发布时间: {list_date if list_date else '未知'}\n")
                
                job_id = job.get('jobId')
                if job_id:
                    f.write(f"   - 职位ID: {job_id}\n")
                
                f.write("\n")
        
        print(f"摘要已保存到 {summary_path}")
        
        return filepath
    
    async def run(self):
        """运行LinkedIn职位爬虫并保存结果。支持多页爬取。"""
        all_jobs = []
        
        print(f"🚀 开始多页面爬取任务...预计爬取 {self.pages_to_crawl} 页")
        
        for page_number in range(self.pages_to_crawl):
            # 计算分页参数
            start_index = page_number * self.results_per_page
            
            # 构建当前页面URL
            if '?' in self.search_url:
                current_url = f"{self.search_url}&start={start_index}"
            else:
                current_url = f"{self.search_url}?start={start_index}"
            
            print(f"\n--- 正在爬取第 {page_number + 1} 页 ---")
            print(f"URL: {current_url}")
            
            try:
                # 爬取当前页面，传入构建的URL
                page_jobs = await self.extract_job_info(url=current_url)
                
                if page_jobs:
                    print(f"✅ 成功提取到 {len(page_jobs)} 条职位信息。")
                    all_jobs.extend(page_jobs)
                else:
                    print("⚠️ 未能从当前页面提取到有效数据，可能已到达末页或页面结构有变。")
                    # 如果某一页没有数据了，提前结束循环
                    break
                
                # 友好爬取：添加延时
                if page_number < self.pages_to_crawl - 1:  # 最后一页不需要等待
                    print("⏳ 等待 3 秒钟后继续...")
                    await asyncio.sleep(3)
                    
            except Exception as e:
                print(f"❌ 爬取第 {page_number + 1} 页时发生错误: {e}")
                # 即使一页失败了，也继续尝试下一页
                continue
        
        # 保存所有页面的结果
        print(f"\n--- 🎉 全部爬取任务完成！---")
        print(f"总共提取到 {len(all_jobs)} 条职位信息。")
        await self.save_results(all_jobs)
        return all_jobs

async def main():
    """运行LinkedIn职位爬虫的主函数。"""
    # 产品经理职位的URL
    search_url = "https://www.linkedin.com/jobs/search/?keywords=product%20manager&origin=SWITCH_SEARCH_VERTICAL"
    
    # 创建并运行爬虫
    crawler = LinkedInJobsCrawler(
        search_url=search_url,
        headless=False,  # 设置为False可以看到浏览器，生产环境设为True
        pages_to_crawl=2  # 爬取两页
    )
    
    jobs = await crawler.run()
    print(f"爬取完成。找到 {len(jobs)} 个职位列表。")

if __name__ == "__main__":
    asyncio.run(main())
