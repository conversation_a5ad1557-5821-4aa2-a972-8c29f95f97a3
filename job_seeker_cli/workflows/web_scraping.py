# /Users/<USER>/Desktop/aus_job/job_seeker_cli/workflows/web_scraping.py
# 网络爬虫工作流：集成职位搜索和详情抓取的完整流程
# 此文件提供从搜索关键词到获取详细职位信息的端到端解决方案

import asyncio
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from job_seeker_cli.workflows.base_workflow import BaseWorkflow
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.ui.user_interaction import UserInteraction
from job_seeker_cli.workflows.job_scraping import JobScrapingWorkflow

class WebScrapingWorkflow(BaseWorkflow):
    """网络爬虫工作流 - 完整的职位搜索和抓取流程"""

    def __init__(self, file_service: FileService, interaction: UserInteraction):
        super().__init__()
        self.file_service = file_service
        self.interaction = interaction
        self.job_scraping_workflow = JobScrapingWorkflow(file_service, interaction)

    def execute(self):
        """执行爬虫工作流"""
        # 显示欢迎信息
        print("\n🕷️ 网络爬虫 - 使用默认配置")
        print("=" * 50)

        # 直接进入平台选择和执行
        self._run_job_search_crawler()



    def _run_job_search_crawler(self):
        """运行职位搜索爬虫"""
        print("\n📋 选择平台")

        # 选择平台（使用默认配置）
        platform = self._select_platform()

        if not platform:
            print("❌ 未选择平台")
            return

        # 显示将使用的默认配置
        self._display_default_config(platform)

        print("\n🚀 开始爬虫")
        # 执行搜索
        try:
            from job_seeker_cli.scripts.crawlers.job_search_crawler import JobSearchCrawler
            crawler = JobSearchCrawler(self.file_service)
            result_file = asyncio.run(crawler.search_with_defaults(platform))

            if result_file:
                print(f"✅ 搜索完成! 结果保存至: {result_file}")

                # 自动继续抓取详情
                self._run_details_for_file(result_file)
            else:
                print("❌ 搜索失败，请检查网络连接")

        except Exception as e:
            print(f"❌ 搜索过程中发生错误: {e}")

    def _select_platform(self) -> Optional[str]:
        """选择爬虫平台"""
        platform_options = {
            "🔗 LinkedIn": "linkedin",
            "🔍 Seek": "seek",
            "🌐 全平台 (LinkedIn + Seek)": "all"
        }

        return self.interaction.get_choice(platform_options)

    def _display_default_config(self, platform: str):
        """显示将使用的默认配置"""
        from job_seeker_cli.config.crawler_defaults import DEFAULT_SEARCH_PARAMS

        print(f"\n📋 使用默认配置:")
        print(f"  🔍 搜索关键词: {DEFAULT_SEARCH_PARAMS['keywords']}")
        print(f"  📍 工作地点: {DEFAULT_SEARCH_PARAMS['location']}")

        if platform == "linkedin":
            print(f"  📄 抓取页数: {DEFAULT_SEARCH_PARAMS['linkedin_pages']}")
        elif platform == "seek":
            print(f"  📄 抓取页数: {DEFAULT_SEARCH_PARAMS['seek_pages']}")
        elif platform == "all":
            print(f"  📄 LinkedIn页数: {DEFAULT_SEARCH_PARAMS['linkedin_pages']}")
            print(f"  📄 Seek页数: {DEFAULT_SEARCH_PARAMS['seek_pages']}")

        print("  ⚙️ 如需修改配置，请编辑 config/crawler_defaults.py\n")



    def _run_details_for_file(self, filename: str):
        """为指定文件运行详情抓取"""
        try:
            # 提取文件名（不含路径）
            base_filename = os.path.basename(filename)

            # 生成输出文件名
            output_filename = base_filename.replace('.json', '_details.json')

            # 创建临时的JobFetcher并执行
            from job_seeker_cli.scripts.fetchers.job_fetcher import JobFetcher
            job_fetcher = JobFetcher(self.file_service)
            job_fetcher.fetch_job_details(base_filename, output_filename)

        except Exception as e:
            print(f"❌ 详情抓取失败: {e}")

    def validate(self) -> bool:
        """验证工作流前置条件"""
        # 检查必要的目录是否存在
        required_dirs = ["input", "processed"]
        for dir_name in required_dirs:
            if not self.file_service.ensure_directory_exists(dir_name):
                print(f"❌ 无法创建必要目录: {dir_name}")
                return False

        return True

    def cleanup(self):
        """清理资源"""
        # 清理可能的浏览器资源
        if hasattr(self, 'job_scraping_workflow'):
            self.job_scraping_workflow.cleanup()
